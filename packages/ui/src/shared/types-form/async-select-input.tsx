import { Typography } from '@mui/joy';
import Box from '@mui/joy/Box';
import Button from '@mui/joy/Button';
import MenuItem from '@mui/material/MenuItem';
import { debounce, unionBy } from 'lodash';
import React from 'react';
import { useLocale } from '@bika/contents/i18n';
import type { NodeListDTOWithoutSpaceID } from '@bika/types/node/dto';
import AddOutlined from '@bika/ui/icons/components/add_outlined';
import { type SelectInputProps, type OptionType, SelectInput } from './select-input';

export function AsyncSelectInput<T extends string, D extends { id: string }>(
  props: Omit<SelectInputProps<T>, 'options'> & {
    loadData: (node: NodeListDTOWithoutSpaceID) => Promise<D[]>;
    renderOption: (data: D) => OptionType;
    pageSize?: number;
    // resetKey 变动时，重新加载数据
    resetKey?: string;
    hideLabel?: boolean;
  },
) {
  const { emptyMsg, loadData, renderOption, pageSize = 20, resetKey, hideLabel } = props;
  const [pageNo, setPageNo] = React.useState(1);
  const [searchTerm, setSearchTerm] = React.useState('');
  const [options, setOptions] = React.useState<OptionType[]>([]);
  const [isLoading, setIsLoading] = React.useState(false);
  const [isMore, setIsMore] = React.useState(true);

  const { t } = useLocale();

  React.useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      const data = await loadData({ pageNo, pageSize, name: searchTerm });
      setIsLoading(false);
      setIsMore(data.length >= pageSize);
      setOptions((prev) => {
        if (pageNo === 1) {
          return data.map(renderOption);
        }
        return unionBy(prev, data.map(renderOption), 'value');
      });
      return data.map(renderOption);
    };
    fetchData();
  }, [pageNo, searchTerm, pageSize, resetKey]);

  React.useEffect(() => {
    setOptions([]);
    setPageNo(1);
  }, [searchTerm, resetKey]);

  const handleSearch = debounce((searchKey: string) => {
    setSearchTerm(searchKey);
  }, 500);

  const isFirstLoading = pageNo === 1 && isLoading;

  return (
    <SelectInput
      {...props}
      options={options}
      handleSearch={handleSearch}
      disabled={isFirstLoading || isLoading}
      iconSize={props.iconSize || 32}
      placeholder={
        isFirstLoading ? (
          <Button loading loadingPosition="start" variant="plain">
            {t.pagination.loading}
          </Button>
        ) : (
          props.placeholder
        )
      }
      footer={
        <Box>
          {options.length === 0 && (
            <>
              <Box display={'flex'} justifyContent={'center'} marginTop={'8px'}>
                <Typography level={'b4'} textColor={'var(--text-secondary)'}>
                  {'empty'}
                </Typography>
              </Box>
            </>
          )}
          {isLoading && (
            <MenuItem value="loading" disabled sx={{ ml: 0.5, color: 'var(--brand)' }}>
              {t.pagination.loading}
            </MenuItem>
          )}
          {isMore && (
            <MenuItem
              value="load-more"
              disabled={isLoading}
              sx={{ color: 'var(--brand)' }}
              onClick={(e) => {
                setPageNo(pageNo + 1);
                e.preventDefault();
              }}
            >
              <AddOutlined currentColor />
              <Typography sx={{ ml: 0.5, color: 'inherit' }}>{t.pagination.load_more}</Typography>
            </MenuItem>
          )}
        </Box>
      }
    />
  );
}
