/**
 * BGrid是AG Grid的封装
 */
import type {
  ValueFormatterParams,
  ValueGetterParams,
  ColDef,
  ValueSetterParams,
  ValueParserParams,
} from '@ag-grid-community/core';
import { iStringParse } from 'basenext/i18n/i-string';
import { produce } from 'immer';
import { isEqual } from 'lodash';
import type React from 'react';
import { useEffect, useMemo, useRef } from 'react';
import { useLocale } from '@bika/contents/i18n/context';
import { consts } from '@bika/domains/shared/client';
import type { ViewGroupArray, CellValue } from '@bika/types/database/bo';
import type { IDatabaseVOContext } from '@bika/types/database/context';
import type { RecordUpdateDTO } from '@bika/types/database/dto';
import type { CellRenderVO, FieldVO, RecordRenderVO, ViewFieldVO } from '@bika/types/database/vo';
import type { INodePermission } from '@bika/types/node/context';
import { useShareContext } from '@bika/types/space/context';
import { useGlobalContext } from '@bika/types/website/context';
import type { CustomCellRendererProps } from '@bika/ui/database/types';
import { useSnackBar, closeSnackbar } from '@bika/ui/snackbar';
import { getFieldColumnDefs } from './field-column-def';
import type { CellRenderVOWithIndex } from './types';
import { SimpleTextCellRenderer } from '../../cells/cell-renderer/simple-text-cell-renderer';
import { withCellEditorWrapper, withCellRendererWrapper } from '../../cells/cell-renderer/withCellRendererWrapper';
import { useCellPasteStore } from '../../cells/store';
import { ShowResourceEditorBtn } from '../../control-bar/toolbar/extra-record-component';
import { buildPrimaryFieldMatchFilter, renderRecordVO } from '../../record-detail/utils';
import { AgGridRoAdapter } from '../ag-database-ro';
import { AgGridConfig } from '../ag-grid-config';
import { ExpandRecordComponent, HiddenExpandRecordComponent } from '../expand-record-button-component';

export const useColumnDefs = ({
  columns,
  spaceId,
  databaseId,
  mirrorId,
  // visibleColumns,
  dataApi,
  rowGroup,
  // hasRowGroup,
  permission,
  disableOperateColumn,
  disableEditing,
  // columns,
  setColumnDefs,
  hasSequenceColumn = false,
  isTemplatePreview,
}: {
  disableOperateColumn?: boolean;
  disableEditing?: boolean;
  columns: ViewFieldVO[];
  spaceId: string;
  databaseId: string;
  hasSequenceColumn?: boolean;
  // columns?: ViewField[];
  mirrorId?: string;
  // visibleColumns: ViewFieldVO[];
  dataApi: IDatabaseVOContext;
  rowGroup?: ViewGroupArray;
  // hasRowGroup: boolean;
  permission: INodePermission | null;
  setColumnDefs: React.Dispatch<React.SetStateAction<ColDef[]>>;
  isTemplatePreview?: boolean;
}) => {
  const { toast } = useSnackBar();
  const { sharing } = useShareContext();
  const { timezone } = useGlobalContext();
  const isTrashPage = useMemo(() => window.location.pathname.includes('trash'), []);
  const isMirror = typeof mirrorId === 'string' && mirrorId.length > 0;

  const { updateRecord } = dataApi.useRecordMutation();

  const { t, lang } = useLocale();

  const { data: linkDatabase } = dataApi.getLinkDatabase(databaseId);

  const updateQueue = useRef<
    {
      recordId: string;
      newCell: RecordUpdateDTO;
      newValue: CellValue;
      setterColumnId: string;
      params: ValueSetterParams<RecordRenderVO, CellValue>;
    }[]
  >([]);
  const isProcessingQueue = useRef(false);

  /**
   * Fetches a linked record when dealing with LINK or ONE_WAY_LINK field types.
   * Searches for matching records in the linked database and returns a properly formatted cell record.
   */
  const fetchLinkedRecord = async ({
    newCell,
    newValue,
    setterColumnId,
  }: {
    newCell: RecordUpdateDTO;
    newValue: CellValue;
    setterColumnId: string;
  }): Promise<RecordUpdateDTO> => {
    const columnType = columns.find((col) => col.id === setterColumnId)?.type;
    const isLinkOrOneWayLink = ['LINK', 'ONE_WAY_LINK'].includes(columnType ?? '');

    if (isLinkOrOneWayLink && typeof newValue === 'string') {
      const primaryField = linkDatabase?.[setterColumnId]?.views[0]?.columns[0];
      const linkedDatabaseId = linkDatabase?.[setterColumnId]?.id;

      if (!primaryField) {
        return newCell;
      }

      let originalValue = newValue;
      const colDefColumn = getFieldColumnDefs(primaryField as unknown as FieldVO, {
        spaceId,
        timeZone: timezone,
        databaseId,
        isTemplatePreview,
      });
      if (colDefColumn.valueParser && typeof colDefColumn.valueParser !== 'string') {
        try {
          const parsedValue = colDefColumn.valueParser({
            newValue,
            oldValue: newValue,
          } as unknown as ValueParserParams<RecordRenderVO, CellValue>);
          if (parsedValue) {
            originalValue = parsedValue;
          }
        } catch (error) {
          console.error(`Failed to parse value for column ${primaryField.id}:`, error);
        }
      }

      const filter = primaryField && buildPrimaryFieldMatchFilter(primaryField, originalValue);

      const data = await dataApi.listRecords({
        databaseId: linkedDatabaseId as string,
        viewId: linkDatabase?.[setterColumnId].views[0]?.id as string,
        filter,
        startRow: 0,
        endRow: 1,
      });

      if (data?.rows?.length) {
        const fetchedCellRecord: RecordUpdateDTO = {
          ...newCell,
          cells: {
            [setterColumnId]: [data.rows[0].id],
          },
        };
        return fetchedCellRecord;
      }
      toast(t.editor.current_data_not_allowed, {
        variant: 'error',
      });
      throw new Error(t.editor.current_data_not_allowed);
    }
    return newCell;
  };

  const { setIsPasting, getPasting } = useCellPasteStore();

  const getSidebarWidth = () => {
    const width = document?.querySelector('#sidebar')?.clientWidth;
    return width;
  };

  const processUpdateQueue = async () => {
    // 如果队列正在处理中，则直接返回
    if (isProcessingQueue.current) return;
    isProcessingQueue.current = true;

    while (updateQueue.current.length > 0) {
      const isLastOne = updateQueue.current.length === 1;

      const { recordId, newCell, newValue, setterColumnId, params } = updateQueue.current.shift()!;
      try {
        const fetchedRecord = await fetchLinkedRecord({ newCell, newValue, setterColumnId });
        const newValResult = await updateRecord(fetchedRecord);

        const rowNode = params.api.getRowNode(recordId)!;
        if (rowNode?.data && newValResult?.cells) {
          const recordVo = newValResult.cells[setterColumnId];
          rowNode?.setData({
            index: (rowNode?.data as unknown as CellRenderVOWithIndex)?.index,
            ...newValResult,
          } as RecordRenderVO);
          if (params?.data?.cells) {
            params.data.cells[setterColumnId] = {
              id: recordVo.id,
              value: recordVo.value,
              data: recordVo.data,
            };
            params.api?.redrawRows?.({ rowNodes: [rowNode] });
          }
        }

        if (isLastOne && getPasting()) {
          setIsPasting(false);
          closeSnackbar('start_paste');
          toast(t.editor.content_paste_successfully, {
            id: 'paste',
            variant: 'paste',
            preventDuplicate: true,
            marginLeft: getSidebarWidth(),
            anchorOrigin: {
              horizontal: 'left',
              vertical: 'bottom',
            },
          });
        } else if (isLastOne) {
          setIsPasting(false);
        }
      } catch (error) {
        // 处理错误，例如记录失败的更新
        console.error(`Failed to update record ${recordId}:`, error);
        if (getPasting()) {
          setIsPasting(false);
          closeSnackbar('start_paste');
          toast(t.editor.content_paste_failure, {
            id: 'paste',
            marginLeft: getSidebarWidth(),
            variant: 'paste',
            preventDuplicate: true,
            anchorOrigin: {
              horizontal: 'left',
              vertical: 'bottom',
            },
          });
        }
      }
    }

    isProcessingQueue.current = false;
  };

  useEffect(() => {
    if (!columns) {
      return;
    }
    const hasRowGroup = rowGroup && rowGroup?.length > 0;
    const columnDefs: ColDef[] = columns?.map?.((column: ViewFieldVO, index) => {
      const currentRowGroup = rowGroup?.find((_item: { fieldId?: string }) => _item.fieldId === column.id);
      const rowGroupIndex = rowGroup?.findIndex((_item: { fieldId?: string }) => _item.fieldId === column.id);

      // 还要重复找做什么?
      // const customColumn = columns?.find((col) => col.id === column.id);

      const colDefColumn = getFieldColumnDefs(column, {
        spaceId,
        timeZone: timezone,
        databaseId,
        isTemplatePreview,
      });
      let editable = colDefColumn.editable ?? permission?.privilege?.abilities?.updateRow;
      if (disableEditing === true) {
        editable = false;
      }

      return {
        headerName: iStringParse(column.name, lang),
        suppressHeaderContextMenu: !editable,
        hide: column.hidden,
        // 是否开启分组
        rowGroup: currentRowGroup != null,
        // 分组顺序, 越低越靠前
        rowGroupIndex: rowGroupIndex !== undefined && rowGroupIndex > -1 ? rowGroupIndex : undefined,
        sort: currentRowGroup?.asc ? 'asc' : 'desc',
        field: column.id,
        type: column.type,
        pinned: !hasSequenceColumn && !hasRowGroup && index === 0 ? 'left' : undefined, // TODO fixme
        lockPosition: !hasSequenceColumn && !hasRowGroup && index === 0,
        width: column.width || AgGridConfig.cellWidth,
        headerComponentParams: {
          disabled: disableEditing,
          fieldType: column.type,
          fieldProperty: column.property,
          isPrimaryField: column.primary,
          field: column,
          permission,
        },
        cellRendererSelector: () => undefined,
        suppressSizeToFit: true,
        fieldProperty: column.property,
        property: column.property,
        filter: false,
        sortable: true,
        keyCreator: (params) => params.data.id ?? '',
        valueFormatter: (params: ValueFormatterParams<RecordRenderVO>) => {
          // 这个可能会被覆盖
          if (!params.data || !params.data.cells || !params.colDef?.field) return '';
          const formatted = params.data.cells[params.colDef.field]?.value ?? '';
          return formatted as string;
        },
        valueSetter: (params: ValueSetterParams<RecordRenderVO, CellValue>) => {
          if (isEqual(params.oldValue, params.newValue)) {
            if (getPasting()) {
              closeSnackbar('start_paste');
              setIsPasting(false);
            }
            return false;
          }

          const setterColumn = columns.find((c: ViewFieldVO) => c.id === params.colDef.field);
          if (!setterColumn) return false;

          const newValue = AgGridRoAdapter.convertToValue(params.newValue ?? null, setterColumn);

          const recordId = params.data.id;
          const newCell = {
            databaseId,
            mirrorId,
            id: recordId,
            cells: { [setterColumn.id]: newValue },
          };

          // 将更新操作添加到队列中
          updateQueue.current.push({ recordId, newCell, newValue, setterColumnId: setterColumn.id, params });

          // 触发处理队列的异步函数
          processUpdateQueue();
          return false;
        },
        ...colDefColumn,
        valueGetter: (params: ValueGetterParams<RecordRenderVO>) => {
          if (!params.data) return '';
          const data = params.data?.cells?.[params.colDef.field ?? '']?.data;
          if (currentRowGroup) {
            if (data == null) {
              return consts.CONST_UNSAFE_ROW_GROUP_VALUE;
            }
          }
          if (colDefColumn.valueGetter && typeof colDefColumn.valueGetter === 'function') {
            return colDefColumn.valueGetter?.(params);
          }
          return data;
        },

        cellRenderer: withCellRendererWrapper(colDefColumn.cellRenderer ?? SimpleTextCellRenderer),
        cellRendererParams: colDefColumn.cellRendererParams,
        enableCellChangeFlash: false,
        editable,
        suppressFillHandle:
          editable === false ||
          permission?.privilege?.abilities?.updateRow === false ||
          colDefColumn.suppressFillHandle,
        suppressPaste: colDefColumn.suppressPaste,
        resizable: ((permission && permission?.privilege?.privilege === 'FULL_ACCESS') || isTrashPage) ?? false,
        ...(currentRowGroup && colDefColumn?.cellEditor && typeof colDefColumn?.cellEditor !== 'string'
          ? { cellEditor: withCellEditorWrapper(colDefColumn.cellEditor) }
          : {}),
      };
    });

    const columnTypes = produce(columnDefs, (draft) => {
      if (hasSequenceColumn) {
        draft.unshift({
          headerName: '',
          width: 72,
          minWidth: 72,
          maxWidth: 72,
          suppressFillHandle: true,
          field: 'index',
          pinned: 'left',
          lockPosition: true,
          suppressMovable: true,
          cellRendererSelector: (_params) =>
            // rows that are not pinned don't use any cell renderer
            undefined,
          resizable: false,
          // suppressFillHandle: true,
          // minWidth: 120,
          // headerCheckboxSelection: () => false,
          // checkboxSelection: (params) => params.data?.index != null,
          headerComponent: null,
          showDisabledCheckboxes: false,
          // headerComponentParams: {
          //   fieldType: 'index',
          // },
          valueGetter: (
            params: ValueGetterParams<
              FieldVO & {
                index: number;
              },
              CellRenderVO['value']
            >,
          ) => params?.data?.index,
          cellRenderer: (props: CustomCellRendererProps<RecordRenderVO>) =>
            props.node?.rowIndex != null ? props.node.rowIndex + 1 : null,
        });
      }
      if (!disableOperateColumn) {
        draft.unshift({
          headerName: '',
          width: 100,
          suppressFillHandle: true,
          minWidth: 100,
          maxWidth: 100,
          field: 'index',
          pinned: 'left',
          lockPosition: true,
          suppressMovable: true,
          cellRendererSelector: (_params) =>
            // rows that are not pinned don't use any cell renderer
            undefined,
          resizable: false,
          // suppressFillHandle: true,
          // minWidth: 120,
          headerComponent: null,
          headerCheckboxSelection: true,
          checkboxSelection: (params) => params.data?.index != null,
          showDisabledCheckboxes: !hasRowGroup,
          headerComponentParams: {
            fieldType: 'index',
          },
          valueGetter: (
            params: ValueGetterParams<
              FieldVO & {
                index: number;
              },
              CellRenderVO['value']
            >,
          ) => params?.data?.index,
          cellRenderer: isTemplatePreview ? HiddenExpandRecordComponent : ExpandRecordComponent,
          valueFormatter: (
            params: ValueFormatterParams<
              FieldVO & {
                index: number;
              },
              CellRenderVO['value']
            >,
          ) => {
            const recId = params?.data?.id;
            if (params?.data) {
              const recordVO = renderRecordVO(params?.data as unknown as RecordRenderVO, columns);

              return JSON.stringify({
                recId,
                dataList: [
                  {
                    value: recId,
                    name: recordVO.name ?? '',
                  },
                ],
              });
            }
            return '';
          },
        });
      }

      if (
        // 没有配置权限时，默认权限，出编辑按钮
        (!permission || (!sharing && permission?.privilege.privilege === 'FULL_ACCESS')) &&
        !disableEditing &&
        !isMirror
      ) {
        draft.push({
          headerComponent: ShowResourceEditorBtn,
          lockPosition: 'right',
          field: 'extract',
          width: 100,
          resizable: false,
          suppressSizeToFit: true,
          suppressFillHandle: true,
          cellClass: () => ['ag-cell', 'ag-cell-add-resource'],
          minWidth: 100,
          maxWidth: 100,
        });
      }
    });
    setColumnDefs(columnTypes);
  }, [
    columns,
    databaseId,
    mirrorId,
    dataApi,
    toast,
    rowGroup,
    setColumnDefs,
    permission,
    disableOperateColumn,
    sharing,
    disableEditing,
  ]);
};
