import React, { useCallback, useEffect, useState } from 'react';
import type { CustomHeaderProps } from '@bika/ui/database/types';
import { IconButton } from '@bika/ui/icon-button';
import CheckboxFilled from '@bika/ui/icons/components/checkbox_filled';
import CheckboxOutlined from '@bika/ui/icons/components/checkbox_outlined';
import { Box } from '@bika/ui/layouts';
import { Tooltip } from '@bika/ui/tooltip';

export interface CheckboxHeaderComponentProps extends CustomHeaderProps {
  /**
   * Optional tooltip text for the checkbox
   */
  tooltipText?: string;
  /**
   * Whether the checkbox is disabled
   */
  disabled?: boolean;
  /**
   * Custom callback when selection state changes
   */
  onSelectionChange?: (isAllSelected: boolean) => void;
}

/**
 * Checkbox Header Component for ag-Grid
 *
 * Features:
 * - Toggle selection of all rows using ag-Grid's API
 * - Shows appropriate checkbox icon based on selection state
 * - Checks selection status through grid API
 * - Supports tooltip and disabled state
 */
export const CheckboxHeaderComponent: React.FC<CheckboxHeaderComponentProps> = ({
  api,
  tooltipText = 'Select All',
  disabled = false,
  onSelectionChange,
}) => {
  const [isAllSelected, setIsAllSelected] = useState(false);
  const [isIndeterminate, setIsIndeterminate] = useState(false);

  // Check selection state using ag-Grid API
  const checkSelectionState = useCallback(() => {
    if (!api) return;

    try {
      // Get all rendered nodes (visible rows)
      const allNodes = api.getRenderedNodes();
      const selectableNodes = allNodes.filter((node) => !node.group && node.selectable !== false);

      if (selectableNodes.length === 0) {
        setIsAllSelected(false);
        setIsIndeterminate(false);
        return;
      }

      const selectedNodes = selectableNodes.filter((node) => node.isSelected());
      const selectedCount = selectedNodes.length;
      const totalCount = selectableNodes.length;

      const allSelected = selectedCount === totalCount && totalCount > 0;
      const someSelected = selectedCount > 0 && selectedCount < totalCount;

      setIsAllSelected(allSelected);
      setIsIndeterminate(someSelected);

      // Notify parent component of selection change
      onSelectionChange?.(allSelected);
    } catch (error) {
      console.warn('Error checking selection state:', error);
      setIsAllSelected(false);
      setIsIndeterminate(false);
    }
  }, [api, onSelectionChange]);

  // Handle checkbox click to toggle selection
  const handleToggleSelection = useCallback(() => {
    if (!api || disabled) return;

    try {
      if (isAllSelected || isIndeterminate) {
        // Deselect all rows
        api.deselectAll();
      } else {
        // Select all rows
        api.selectAll();
      }

      // Check selection state after action
      setTimeout(() => {
        checkSelectionState();
      }, 100);
    } catch (error) {
      console.warn('Error toggling selection:', error);
    }
  }, [api, disabled, isAllSelected, isIndeterminate, checkSelectionState]);

  // Check selection state on mount and when API changes
  useEffect(() => {
    if (!api) return;
    checkSelectionState();
  }, [api, checkSelectionState]);

  // Determine which icon to show
  const getCheckboxIcon = () => {
    if (isAllSelected) {
      return <CheckboxFilled color="var(--brand)" />;
    }
    if (isIndeterminate) {
      return <CheckboxFilled color="var(--brand)" />;
    }
    return <CheckboxOutlined color="var(--text-secondary)" />;
  };

  const checkboxButton = (
    <IconButton
      onClick={handleToggleSelection}
      disabled={disabled}
      sx={{
        padding: '4px',
        borderRadius: '4px',
        '&:hover': {
          backgroundColor: disabled ? 'transparent' : 'var(--hover)',
        },
        '&:disabled': {
          opacity: 0.5,
          cursor: 'not-allowed',
        },
      }}
    >
      {getCheckboxIcon()}
    </IconButton>
  );

  return (
    <Box display="flex" alignItems="center" justifyContent="center" height="100%" width="100%">
      {tooltipText && !disabled ? (
        <Tooltip title={tooltipText} placement="top">
          {checkboxButton}
        </Tooltip>
      ) : (
        checkboxButton
      )}
    </Box>
  );
};

export default CheckboxHeaderComponent;
