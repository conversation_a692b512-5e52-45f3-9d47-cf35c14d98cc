# CheckboxHeaderComponent

A reusable checkbox header component for ag-Grid that provides "select all" functionality.

## Features

- ✅ Toggle selection of all rows using ag-Grid's API
- ✅ Shows appropriate checkbox icon based on selection state (empty, filled, indeterminate)
- ✅ Checks selection status through grid API (`isAllSelected`)
- ✅ Supports tooltip and disabled state
- ✅ Follows existing codebase patterns and styling
- ✅ TypeScript support with proper type definitions
- ✅ Event listeners for real-time selection state updates

## Props

The component extends `CustomHeaderProps` from ag-Grid and accepts the following additional props:

| Prop | Type | Default | Description |
|------|------|---------|-------------|
| `tooltipText` | `string` | `'Select All'` | Optional tooltip text for the checkbox |
| `disabled` | `boolean` | `false` | Whether the checkbox is disabled |
| `onSelectionChange` | `(isAllSelected: boolean) => void` | `undefined` | Custom callback when selection state changes |

## Usage

### Basic Usage

```tsx
import { CheckboxHeaderComponent } from './checkbox-header-component';

// In your column definition
const columnDef: ColDef = {
  headerName: '',
  field: 'selection',
  width: 50,
  headerComponent: CheckboxHeaderComponent,
  headerComponentParams: {
    tooltipText: 'Select all rows',
    disabled: false,
    onSelectionChange: (isAllSelected: boolean) => {
      console.log('All rows selected:', isAllSelected);
    },
  },
};
```

### With ag-Grid Configuration

```tsx
import { createCheckboxColumnDef } from './checkbox-header-example';

const gridOptions = {
  columnDefs: [
    createCheckboxColumnDef(), // Adds the checkbox column
    { headerName: 'Name', field: 'name' },
    { headerName: 'Email', field: 'email' },
  ],
  rowSelection: 'multiple',
  rowMultiSelectWithClick: true,
};
```

### Advanced Usage

```tsx
const customColumnDef: ColDef = {
  headerComponent: CheckboxHeaderComponent,
  headerComponentParams: {
    tooltipText: 'Toggle all items',
    disabled: false,
    onSelectionChange: (isAllSelected) => {
      // Custom logic when selection changes
      if (isAllSelected) {
        console.log('All items selected');
      } else {
        console.log('Not all items selected');
      }
    },
  },
};
```

## Selection States

The component handles three selection states:

1. **None Selected**: Shows empty checkbox (`CheckboxOutlined`)
2. **All Selected**: Shows filled checkbox (`CheckboxFilled`) with brand color
3. **Some Selected (Indeterminate)**: Shows filled checkbox (`CheckboxFilled`) with brand color

## API Integration

The component uses the following ag-Grid APIs:

- `api.getRenderedNodes()` - Get all visible rows
- `api.selectAll()` - Select all rows
- `api.deselectAll()` - Deselect all rows
- Event listeners for `selectionChanged`, `rowDataChanged`, and `modelUpdated`

## Styling

The component follows the existing design system:

- Uses `var(--brand)` for selected state
- Uses `var(--text-secondary)` for unselected state
- Uses `var(--hover)` for hover effects
- Supports disabled state with reduced opacity

## Testing

The component includes comprehensive tests covering:

- Basic rendering
- Selection functionality
- Disabled state
- Event listener management
- Error handling
- Callback functionality

Run tests with:
```bash
npm test checkbox-header-component.test.tsx
```

## Files

- `checkbox-header-component.tsx` - Main component
- `checkbox-header-example.tsx` - Usage examples
- `__tests__/checkbox-header-component.test.tsx` - Test suite
- `checkbox-header-component.md` - This documentation
