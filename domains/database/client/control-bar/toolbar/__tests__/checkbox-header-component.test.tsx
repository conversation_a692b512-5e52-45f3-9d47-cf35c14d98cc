import React from 'react';
import { render, fireEvent, screen } from '@testing-library/react';
import { CheckboxHeaderComponent } from '../checkbox-header-component';
import type { GridApi } from '@bika/ui/database/types';

// Mock ag-Grid API
const createMockGridApi = (overrides: Partial<GridApi> = {}): GridApi => {
  const mockNodes = [
    { id: '1', isSelected: () => false, group: false, selectable: true },
    { id: '2', isSelected: () => false, group: false, selectable: true },
    { id: '3', isSelected: () => false, group: false, selectable: true },
  ];

  return {
    getRenderedNodes: jest.fn(() => mockNodes),
    selectAll: jest.fn(),
    deselectAll: jest.fn(),
    ...overrides,
  } as unknown as GridApi;
};

describe('CheckboxHeaderComponent', () => {
  let mockApi: GridApi;

  beforeEach(() => {
    mockApi = createMockGridApi();
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('renders checkbox icon', () => {
    render(<CheckboxHeaderComponent api={mockApi} />);
    
    // Should render an icon button
    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
  });

  it('shows tooltip when provided', () => {
    const tooltipText = 'Select all items';
    render(<CheckboxHeaderComponent api={mockApi} tooltipText={tooltipText} />);
    
    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
  });

  it('calls selectAll when clicking unchecked checkbox', () => {
    render(<CheckboxHeaderComponent api={mockApi} />);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    expect(mockApi.selectAll).toHaveBeenCalledTimes(1);
  });

  it('calls deselectAll when clicking checked checkbox', () => {
    // Mock all nodes as selected
    const selectedNodes = [
      { id: '1', isSelected: () => true, group: false, selectable: true },
      { id: '2', isSelected: () => true, group: false, selectable: true },
      { id: '3', isSelected: () => true, group: false, selectable: true },
    ];
    
    const apiWithSelectedNodes = createMockGridApi({
      getRenderedNodes: jest.fn(() => selectedNodes),
    });

    render(<CheckboxHeaderComponent api={apiWithSelectedNodes} />);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    expect(apiWithSelectedNodes.deselectAll).toHaveBeenCalledTimes(1);
  });

  it('does not call selection methods when disabled', () => {
    render(<CheckboxHeaderComponent api={mockApi} disabled={true} />);
    
    const button = screen.getByRole('button');
    fireEvent.click(button);
    
    expect(mockApi.selectAll).not.toHaveBeenCalled();
    expect(mockApi.deselectAll).not.toHaveBeenCalled();
  });

  it('calls onSelectionChange callback when provided', () => {
    const onSelectionChange = jest.fn();
    render(<CheckboxHeaderComponent api={mockApi} onSelectionChange={onSelectionChange} />);
    
    // The callback should be called during initial state check
    expect(onSelectionChange).toHaveBeenCalledWith(false);
  });

  it('handles missing API gracefully', () => {
    // Should not throw when api is undefined
    expect(() => {
      render(<CheckboxHeaderComponent api={undefined as any} />);
    }).not.toThrow();
  });

  it('checks selection state on mount', () => {
    render(<CheckboxHeaderComponent api={mockApi} />);

    // Should check selection state when component mounts
    expect(mockApi.getRenderedNodes).toHaveBeenCalled();
  });
});
