import React from 'react';
import type { ColDef } from '@bika/ui/database/types';
import { CheckboxHeaderComponent } from './checkbox-header-component';

/**
 * Example usage of CheckboxHeaderComponent
 * 
 * This shows how to integrate the CheckboxHeaderComponent into ag-Grid column definitions
 */

// Example column definition using the CheckboxHeaderComponent
export const createCheckboxColumnDef = (): ColDef => ({
  headerName: '',
  field: 'selection',
  width: 50,
  minWidth: 50,
  maxWidth: 50,
  resizable: false,
  sortable: false,
  filter: false,
  checkboxSelection: true,
  headerCheckboxSelection: true,
  pinned: 'left',
  lockPosition: true,
  // Use our custom header component
  headerComponent: CheckboxHeaderComponent,
  headerComponentParams: {
    tooltipText: 'Select all rows',
    disabled: false,
    onSelectionChange: (isAllSelected: boolean) => {
      console.log('Selection state changed:', isAllSelected);
    },
  },
});

// Alternative approach: Custom header component for any column
export const createCustomCheckboxColumn = (): ColDef => ({
  headerName: 'Select',
  field: 'customSelection',
  width: 80,
  resizable: false,
  sortable: false,
  filter: false,
  headerComponent: CheckboxHeaderComponent,
  headerComponentParams: {
    tooltipText: 'Toggle all row selection',
    disabled: false,
    onSelectionChange: (isAllSelected: boolean) => {
      // Custom logic when selection changes
      console.log('All rows selected:', isAllSelected);
    },
  },
  // Optional: Custom cell renderer for the column content
  cellRenderer: () => null, // Hide cell content since this is just for header selection
});

/**
 * Example of how to use in a grid configuration
 */
export const exampleGridConfig = {
  columnDefs: [
    createCheckboxColumnDef(), // Selection column
    {
      headerName: 'Name',
      field: 'name',
      flex: 1,
    },
    {
      headerName: 'Email',
      field: 'email',
      flex: 1,
    },
    {
      headerName: 'Status',
      field: 'status',
      width: 120,
    },
  ],
  rowSelection: 'multiple' as const,
  rowMultiSelectWithClick: true,
  suppressRowClickSelection: false,
};

export default CheckboxHeaderComponent;
